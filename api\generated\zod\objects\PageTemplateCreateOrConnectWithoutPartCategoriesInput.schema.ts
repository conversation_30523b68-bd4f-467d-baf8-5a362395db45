/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { TemplateVersionSchema } from '../models/TemplateVersion.schema';

import { CategoryTemplateConfigSchema } from '../models/CategoryTemplateConfig.schema';

import { PartTemplateConfigSchema } from '../models/PartTemplateConfig.schema';

import { CatalogItemTemplateConfigSchema } from '../models/CatalogItemTemplateConfig.schema'; import { z } from 'zod';
import { PageTemplateWhereUniqueInputObjectSchema } from './PageTemplateWhereUniqueInput.schema';
import { PageTemplateCreateWithoutPartCategoriesInputObjectSchema } from './PageTemplateCreateWithoutPartCategoriesInput.schema';
import { PageTemplateUncheckedCreateWithoutPartCategoriesInputObjectSchema } from './PageTemplateUncheckedCreateWithoutPartCategoriesInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PageTemplateCreateOrConnectWithoutPartCategoriesInput>;
export const PageTemplateCreateOrConnectWithoutPartCategoriesInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PageTemplateWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PageTemplateCreateWithoutPartCategoriesInputObjectSchema), z.lazy(() => PageTemplateUncheckedCreateWithoutPartCategoriesInputObjectSchema)])
}).strict() as SchemaType;
