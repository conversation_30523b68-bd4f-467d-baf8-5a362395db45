<template>
  <div class="w-full">
    <VCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-semibold">Редактировать шаблон страницы</h2>
            <p class="text-sm text-gray-600 mt-1">
              Настройка шаблона для генерации страниц
              {{ getKindLabel(form.kind) }}
            </p>
          </div>
          <div class="flex items-center space-x-3">
            <VButton label="Предпросмотр" icon="Eye" variant="outline" @click="togglePreview"
              :loading="loadingPreview" />
            <VButton label="Удалить" icon="Trash2" variant="destructive" @click="onDelete" :loading="deleteLoading" />
            <VButton label="Назад к списку" icon="ArrowLeft" @click="navigate('/admin/templates')" variant="outline" />
          </div>
        </div>
      </template>
      <template #content>
        <div v-if="loading" class="flex justify-center py-12">
          <div class="text-gray-500">Загрузка шаблона...</div>
        </div>

        <div v-else-if="error" class="text-center py-12">
          <div class="text-red-600 mb-4">{{ error }}</div>
          <VButton label="Повторить" icon="RefreshCw" @click="loadTemplate" />
        </div>

        <div v-else class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Форма редактирования -->
          <div class="space-y-6">
            <!-- Система вкладок -->
            <VTabs v-model:activeIndex="activeTab" class="w-full">
              <VTabList class="flex border-b border-gray-200">
                <VTab class="px-4 py-2 text-sm font-medium">Основные</VTab>
                <VTab class="px-4 py-2 text-sm font-medium">Компоновка</VTab>
                <VTab class="px-4 py-2 text-sm font-medium">Атрибуты</VTab>
                <VTab class="px-4 py-2 text-sm font-medium">SEO</VTab>
                <VTab class="px-4 py-2 text-sm font-medium">Привязки</VTab>
              </VTabList>

              <VTabPanels class="mt-6">
                <!-- Вкладка: Основные настройки -->
                <VTabPanel>
                  <form @submit.prevent="onSubmit" class="space-y-6">
              <!-- Основные поля -->
              <VCard>
                <template #header>
                  <h3 class="text-md font-medium">Основные настройки</h3>
                </template>
                <template #content>
                  <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div class="space-y-2">
                        <label class="text-sm font-medium">Название *</label>
                        <VInputText v-model="form.name" placeholder="Введите название шаблона"
                          :class="{ 'border-red-500': errors.name }" />
                        <span v-if="errors.name" class="text-sm text-red-500">{{
                          errors.name
                          }}</span>
                      </div>

                      <div class="space-y-2">
                        <label class="text-sm font-medium">Тип *</label>
                        <div>
                          <VSelect v-model="form.kind" :options="kindOptions" placeholder="Выберите тип"
                            optionLabel="label" optionValue="value" :class="{ 'border-red-500': errors.kind }"
                            @change="onKindChange" />
                        </div>
                        <span v-if="errors.kind" class="text-sm text-red-500">{{
                          errors.kind
                          }}</span>
                      </div>
                    </div>

                    <div class="space-y-2">
                      <label class="text-sm font-medium">Описание</label>
                      <div>
                        <VTextarea
                          v-model="form.description"
                          placeholder="Введите описание шаблона"
                          :invalid="!!errors.description"
                          class="bg-gray-100 p-2 rounded border border-gray-200"
                        />
                      </div>
                      <span v-if="errors.description" class="text-sm text-red-500">{{ errors.description }}</span>
                    </div>

                    <div class="space-y-2">
                      <label class="text-sm font-medium">Категория запчасти</label>
                      <div>
                        <VAutoComplete v-model="categoryQuery" :suggestions="categorySuggestions" optionLabel="name"
                          placeholder="Выберите категорию (опционально)" @complete="onCompleteCategory"
                          @item-select="onSelectCategory" @clear="onClearCategory" dropdown show-clear
                          empty-message="Категории не найдены" />
                      </div>
                      <small class="text-gray-500">
                        Привязка к конкретной категории (для специализированных
                        шаблонов)
                      </small>
                    </div>

                    <div class="flex items-center gap-2">
                      <div class="flex items-center gap-2">
                        <VCheckbox v-model="form.isDefault" :binary="true" inputId="template-default" />
                        <label for="template-default">Шаблон по умолчанию</label>
                      </div>
                      <div class="flex items-center gap-2">
                        <VCheckbox v-model="form.isActive" :binary="true" inputId="template-active" />
                        <label for="template-active">Активен</label>
                      </div>
                    </div>
                  </div>
                </template>
              </VCard>

              <!-- Конфигурация шаблона категории -->
              <VCard v-if="form.kind === 'CATEGORY'">
                <template #header>
                  <h3 class="text-md font-medium">
                    Конфигурация шаблона категории
                  </h3>
                </template>
                <template #content>
                  <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div class="space-y-2">
                        <label class="text-sm font-medium">H1 заголовок *</label>
                        <VEditor v-model="form.categoryConfig.h1" placeholder="Например: {{category.name}}"
                          class="min-h-[80px]" />
                        <span v-if="errors.h1" class="text-sm text-red-500">{{
                          errors.h1
                          }}</span>
                      </div>

                      <div class="space-y-2">
                        <label class="text-sm font-medium">H2 заголовок</label>
                        <VEditor v-model="form.categoryConfig.h2" placeholder="Например: {{category.description}}"
                          class="min-h-[80px]" />
                        <span v-if="errors.h2" class="text-sm text-red-500">{{
                          errors.h2
                          }}</span>
                      </div>
                    </div>

                    <div class="space-y-2">
                      <label class="text-sm font-medium">Описание</label>
                      <VEditor v-model="form.categoryConfig.description" placeholder="Описание категории для SEO"
                        class="min-h-[120px]" />
                    </div>

                    <div class="space-y-2">
                      <label class="text-sm font-medium">Подвал</label>
                      <VEditor v-model="form.categoryConfig.footer"
                        placeholder="Дополнительная информация в конце страницы" class="min-h-[80px]" />
                    </div>

                    <div class="space-y-4">
                      <div class="space-y-2">
                        <label class="text-sm font-medium">Фильтры</label>
                        <AttributePicker v-model="categoryFiltersModel" label="Атрибуты для фильтрации"
                          placeholder="Добавить атрибут для фильтра" />
                      </div>

                      <div class="space-y-2">
                        <label class="text-sm font-medium">Атрибуты товаров</label>
                        <AttributePicker v-model="categoryProductAttrsModel" label="Атрибуты в карточках товаров"
                          placeholder="Добавить атрибут для отображения" />
                      </div>
                    </div>
                  </div>
                </template>
              </VCard>

              <!-- Конфигурация шаблона запчасти -->
              <VCard v-if="form.kind === 'PART'">
                <template #header>
                  <h3 class="text-md font-medium">
                    Конфигурация шаблона запчасти
                  </h3>
                </template>
                <template #content>
                  <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div class="space-y-2">
                        <label class="text-sm font-medium">H1 заголовок *</label>
                        <VEditor v-model="form.partConfig.h1" placeholder="Например: {{part.name}}"
                          class="min-h-[80px]" />
                        <span v-if="errors.h1" class="text-sm text-red-500">{{
                          errors.h1
                          }}</span>
                      </div>

                      <div class="space-y-2">
                        <label class="text-sm font-medium">H2 заголовок</label>
                        <VEditor v-model="form.partConfig.h2" placeholder="Например: Характеристики {{part.name}}"
                          class="min-h-[80px]" />
                        <span v-if="errors.h2" class="text-sm text-red-500">{{
                          errors.h2
                          }}</span>
                      </div>
                    </div>

                    <div class="space-y-2">
                      <label class="text-sm font-medium">Атрибуты</label>
                      <AttributePicker v-model="form.partConfig.attributes" label="Атрибуты для отображения"
                        placeholder="Добавить атрибут" />
                    </div>
                  </div>
                </template>
              </VCard>

              <!-- Конфигурация шаблона каталожной позиции -->
              <VCard v-if="form.kind === 'CATALOG_ITEM'">
                <template #header>
                  <h3 class="text-md font-medium">
                    Конфигурация шаблона каталожной позиции
                  </h3>
                </template>
                <template #content>
                  <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div class="space-y-2">
                        <label class="text-sm font-medium">H1 заголовок *</label>
                        <VEditor v-model="form.catalogItemConfig.h1" placeholder="Например: {{item.sku}} {{brand.name}}"
                          class="min-h-[80px]" />
                        <span v-if="errors.h1" class="text-sm text-red-500">{{
                          errors.h1
                          }}</span>
                      </div>

                      <div class="space-y-2">
                        <label class="text-sm font-medium">H2 заголовок</label>
                        <VEditor v-model="form.catalogItemConfig.h2" placeholder="Например: {{item.description}}"
                          class="min-h-[80px]" />
                        <span v-if="errors.h2" class="text-sm text-red-500">{{
                          errors.h2
                          }}</span>
                      </div>
                    </div>

                    <div class="space-y-2">
                      <label class="text-sm font-medium">Атрибуты</label>
                      <AttributePicker v-model="form.catalogItemConfig.attributes" label="Атрибуты для отображения"
                        placeholder="Добавить атрибут" />
                    </div>
                  </div>
                </template>
              </VCard>

                    <!-- Кнопки действий -->
                    <div class="flex justify-end space-x-3 pt-4">
                      <VButton type="button" label="Отмена" variant="outline" @click="navigate('/admin/templates')" />
                      <VButton type="submit" label="Сохранить" :loading="saveLoading" icon="Save" />
                    </div>
                  </form>
                </VTabPanel>

                <!-- Вкладка: Компоновка страницы -->
                <VTabPanel>
                  <LayoutEditor
                    v-model="activeLayoutConfig"
                    :disabled="saveLoading"
                  />
                </VTabPanel>

                <!-- Вкладка: Атрибуты -->
                <VTabPanel>
                  <AttributePicker
                    v-model="activeAttributeConfig"
                    :mode="'groups'"
                    label="Группы атрибутов"
                    description="Организуйте атрибуты в логические группы"
                    :disabled="saveLoading"
                  />
                </VTabPanel>

                <!-- Вкладка: SEO -->
                <VTabPanel>
                  <SEOConfigurator
                    v-model="activeSEOConfig"
                    :kind="form.kind"
                    :disabled="saveLoading"
                    :preview-data="getSEOPreviewData()"
                  />
                </VTabPanel>

                <!-- Вкладка: Привязки -->
                <VTabPanel>
                  <TemplateAssignments
                    :template="form"
                    :disabled="saveLoading"
                    @update:template="updateTemplate"
                  />
                </VTabPanel>
              </VTabPanels>
            </VTabs>
          </div>

          <!-- Правая панель: предпросмотр и справка -->
          <div class="space-y-6 lg:sticky top-4">
            <!-- Предпросмотр -->
            <VCard v-if="showPreview">
              <template #header>
                <div class="flex items-center justify-between">
                  <h3 class="text-md font-medium">Предпросмотр</h3>
                  <VButton outlined size="small" @click="refreshPreview" :loading="loadingPreview"
                    v-tooltip="'Обновить предпросмотр'">
                    <RefreshCwIcon class="w-4 h-4" />
                  </VButton>
                </div>
              </template>
              <template #content>
                <div v-if="loadingPreview" class="flex justify-center py-8">
                  <div class="text-gray-500">Загрузка предпросмотра...</div>
                </div>

                <div v-else-if="previewError" class="text-center py-8">
                  <div class="text-red-600 mb-4">{{ previewError }}</div>
                  <VButton label="Повторить" icon="RefreshCw" size="sm" @click="refreshPreview" />
                </div>

                <PreviewRenderer v-else :kind="form.kind" :config="getActiveConfigUtil(form)"
                  :previewData="previewData" />
              </template>
            </VCard>

            <!-- Справка по переменным -->
            <TemplateHelp :kind="form.kind" :selectedAttributeNames="selectedAttributeNames" />
          </div>
        </div>
      </template>
    </VCard>

    <!-- Диалог подтверждения удаления -->
    <VConfirmDialog v-model:visible="deleteDialog.visible" :header="`Удалить шаблон '${form.name}'?`"
      message="Это действие нельзя отменить. Шаблон будет удален навсегда." icon="AlertTriangle" acceptLabel="Удалить"
      rejectLabel="Отмена" acceptClass="p-button-danger" @accept="() => confirmDelete()" @reject="() => cancelDelete()" />
  </div>
</template>

<script setup lang="ts">
import "quill/dist/quill.snow.css";

import { ref, computed, onMounted, watch } from "vue";
import VCard from "@/volt/Card.vue";
import VButton from "@/volt/Button.vue";
import VInputText from "@/volt/InputText.vue";
import VEditor from "@/volt/Editor.vue";
import VSelect from "@/volt/Select.vue";
import VCheckbox from "@/volt/Checkbox.vue";
import VTabs from "@/volt/Tabs.vue";
import VTabList from "@/volt/TabList.vue";
import VTab from "@/volt/Tab.vue";
import VTabPanels from "@/volt/TabPanels.vue";
import VTabPanel from "@/volt/TabPanel.vue";
import TemplateHelp from "./components/TemplateHelp.vue";
import VAutoComplete from "@/volt/AutoComplete.vue";
import VConfirmDialog from "@/volt/ConfirmDialog.vue";
import VTextarea from "@/volt/Textarea.vue";
import { navigate } from "astro:transitions/client";
import { useTrpc } from "@/composables/useTrpc";
import AttributePicker from "./components/AttributePicker.vue";
import PreviewRenderer from "./components/PreviewRenderer.vue";
import LayoutEditor from "./components/LayoutEditor.vue";
import SEOConfigurator from "./components/SEOConfigurator.vue";
import TemplateAssignments from "./components/TemplateAssignments.vue";
import type {
  PageTemplate,
  TemplateFormState,
  TemplateFormErrors,
  PartCategory,
  TemplatePreviewData,
  AttributeListConfig,
} from "@/types/templates";
import {
  TEMPLATE_KIND_OPTIONS,
  createInitialFormState,
  templateToFormState,
  validateTemplateForm,
  hasValidationErrors,
  prepareUpdateData,
  getActiveConfig as getActiveConfigUtil,
  getTemplateKindLabel,
} from "@/utils/templates";
import { RefreshCwIcon } from "lucide-vue-next";

interface Props {
  id: string;
}

const props = defineProps<Props>();
const trpc = useTrpc();

// Состояние компонента
const loading = ref(true);
const saveLoading = ref(false);
const deleteLoading = ref(false);
const loadingPreview = ref(false);
const error = ref<string | null>(null);
const previewError = ref<string | null>(null);
const activeTab = ref(0);

// Состояние формы
const form = ref<TemplateFormState>(createInitialFormState());
const errors = ref<TemplateFormErrors>({});

// Предпросмотр
const showPreview = ref(false);
const previewData = ref<TemplatePreviewData | null>(null);

// Категории
const categoryQuery = ref("");
const categorySuggestions = ref<PartCategory[]>([]);

// Диалог удаления
const deleteDialog = ref({
  visible: false,
});

// Опции для селектов
const kindOptions = TEMPLATE_KIND_OPTIONS;

// Динамический список выбранных атрибутов для справочника
const selectedAttributeNames = computed<string[]>(() => {
  const kind = form.value.kind;
  if (kind === "CATEGORY") {
    const names = form.value.categoryConfig.productAttrs?.attributeNames ?? [];
    const filterNames = form.value.categoryConfig.filters?.attributeNames ?? [];
    return Array.from(new Set([...names, ...filterNames]));
  }
  if (kind === "PART") {
    return form.value.partConfig.attributes?.attributeNames ?? [];
  }
  if (kind === "CATALOG_ITEM") {
    return form.value.catalogItemConfig.attributes?.attributeNames ?? [];
  }
  return [];
});

// Прокси для CATEGORY, чтобы v-model у AttributePicker всегда работал с объектом
const categoryFiltersModel = computed<AttributeListConfig>({
  get: () =>
    form.value.categoryConfig.filters ?? {
      attributeNames: [],
      sortOrder: [],
      withUnits: false,
    },
  set: (val) => {
    form.value.categoryConfig.filters = val;
  },
});

const categoryProductAttrsModel = computed<AttributeListConfig>({
  get: () =>
    form.value.categoryConfig.productAttrs ?? {
      attributeNames: [],
      sortOrder: [],
      withUnits: true,
    },
  set: (val) => {
    form.value.categoryConfig.productAttrs = val;
  },
});

// Вычисляемые свойства
const getKindLabel = computed(
  () => (kind: string) => getTemplateKindLabel(kind as any)
);

// Методы загрузки данных
async function loadTemplate() {
  try {
    loading.value = true;
    error.value = null;

    const data = await trpc.client.pageTemplates.byId.query({ id: props.id });

    if (data) {
      form.value = templateToFormState(data as PageTemplate);

      // Загружаем информацию о категории если она привязана
      if (data.partCategoryId) {
        try {
          const category = await trpc.client.crud.partCategory.findFirst.query({
            where: { id: data.partCategoryId },
          });
          if (category) {
            categoryQuery.value = category.name;
          }
        } catch (err) {
          console.warn("Не удалось загрузить информацию о категории:", err);
        }
      }
    } else {
      error.value = "Шаблон не найден";
    }
  } catch (err: any) {
    error.value = err?.message || "Ошибка загрузки шаблона";
  } finally {
    loading.value = false;
  }
}

// Методы сохранения и валидации
async function onSubmit() {
  try {
    saveLoading.value = true;
    errors.value = {};

    // Валидация формы
    const validationErrors = validateTemplateForm(form.value);
    if (hasValidationErrors(validationErrors)) {
      errors.value = validationErrors;
      return;
    }

    // Подготовка данных для отправки
    const updateData = prepareUpdateData(props.id, form.value);

    const result = await trpc.client.pageTemplates.update.mutate(updateData);

    if (result) {
      // Показываем уведомление об успехе
      await trpc.execute(() => Promise.resolve(result), {
        success: { title: "Сохранено", message: "Шаблон успешно обновлен" },
      });

      navigate("/admin/templates");
    }
  } catch (err: any) {
    console.error("Ошибка сохранения:", err);
    // Ошибка будет показана через trpc.execute автоматически
  } finally {
    saveLoading.value = false;
  }
}

// Методы удаления
function onDelete() {
  deleteDialog.value.visible = true;
}

async function confirmDelete() {
  try {
    deleteLoading.value = true;

    await trpc.client.pageTemplates.delete.mutate({ id: props.id });

    // Показываем уведомление об успехе
    await trpc.execute(() => Promise.resolve(true), {
      success: { title: "Удалено", message: "Шаблон успешно удален" },
    });

    navigate("/admin/templates");
  } catch (err: any) {
    console.error("Ошибка удаления:", err);
  } finally {
    deleteLoading.value = false;
    cancelDelete();
  }
}

function cancelDelete() {
  deleteDialog.value.visible = false;
}

// Работа с категориями
async function loadAllCategories() {
  try {
    const res = await trpc.client.crud.partCategory.findMany.query({
      orderBy: { name: "asc" },
      take: 200, // Загружаем больше категорий для dropdown
    });
    categorySuggestions.value = res || [];
  } catch (error) {
    console.error("Ошибка загрузки категорий:", error);
    categorySuggestions.value = [];
  }
}

async function onCompleteCategory(e: any) {
  try {
    const res = await trpc.client.crud.partCategory.findMany.query({
      where: { name: { contains: e.query, mode: "insensitive" } },
      orderBy: { name: "asc" },
      take: 10,
    });
    categorySuggestions.value = res || [];
  } catch (error) {
    console.error("Ошибка загрузки категорий:", error);
    categorySuggestions.value = [];
  }
}

function onSelectCategory(e: any) {
  form.value.partCategoryId = e.value?.id;
}

function onClearCategory() {
  form.value.partCategoryId = undefined;
  categoryQuery.value = "";
}

// Обработчики изменений
function onKindChange() {
  // При смене типа шаблона сбрасываем ошибки
  errors.value = {};

  // Автоматически обновляем предпросмотр если он открыт
  if (showPreview.value) {
    refreshPreview();
  }
}

// Предпросмотр
function togglePreview() {
  showPreview.value = !showPreview.value;
  if (showPreview.value && !previewData.value) {
    refreshPreview();
  }
}

async function refreshPreview() {
  try {
    loadingPreview.value = true;
    previewError.value = null;

    // Валидируем форму перед предпросмотром
    const validationErrors = validateTemplateForm(form.value);
    if (hasValidationErrors(validationErrors)) {
      previewError.value = "Исправьте ошибки в форме для предпросмотра";
      return;
    }

    let data = null;

    switch (form.value.kind) {
      case "CATEGORY":
        // Получаем данные категории для предпросмотра
        const categoryId = form.value.partCategoryId;
        if (categoryId) {
          const category = await trpc.client.crud.partCategory.findFirst.query({
            where: { id: categoryId },
          });
          if (category) {
            data = await trpc.client.pageTemplates.renderCategory.query({
              slug: category.slug,
            });
          }
        } else {
          // Используем первую доступную категорию
          const categories = await trpc.client.crud.partCategory.findMany.query(
            { take: 1 }
          );
          if (categories.length > 0) {
            data = await trpc.client.pageTemplates.renderCategory.query({
              slug: categories[0].slug,
            });
          }
        }
        break;

      case "PART":
        // Получаем данные запчасти для предпросмотра
        const parts = await trpc.client.crud.part.findMany.query({
          take: 1,
          include: { attributes: true, partCategory: true },
        });
        if (parts.length > 0) {
          data = await trpc.client.pageTemplates.renderPart.query({
            id: parts[0].id,
          });
        }
        break;

      case "CATALOG_ITEM":
        // Получаем данные каталожной позиции для предпросмотра
        const items = await trpc.client.crud.catalogItem.findMany.query({
          take: 1,
          include: { attributes: true, brand: true },
        });
        if (items.length > 0) {
          data = await trpc.client.pageTemplates.renderCatalogItem.query({
            id: items[0].id,
          });
        }
        break;
    }

    // Подменяем конфигурацию в данных предпросмотра на текущую из формы
    if (data && data.template) {
      data.template.config = getActiveConfigUtil(form.value);
    }

    previewData.value = data;
  } catch (err: any) {
    previewError.value = err?.message || "Ошибка загрузки предпросмотра";
  } finally {
    loadingPreview.value = false;
  }
}

// Утилитарные методы
// Используем импортированную утилиту getActiveConfigUtil(form) непосредственно в шаблоне/коде

// Функции для работы с новыми конфигурациями
function getActiveLayoutConfig() {
  switch (form.value.kind) {
    case 'CATEGORY':
      if (!form.value.categoryConfig.layout) {
        form.value.categoryConfig.layout = { components: [] };
      }
      return form.value.categoryConfig.layout;
    case 'PART':
      if (!form.value.partConfig.layout) {
        form.value.partConfig.layout = { components: [] };
      }
      return form.value.partConfig.layout;
    case 'CATALOG_ITEM':
      if (!form.value.catalogItemConfig.layout) {
        form.value.catalogItemConfig.layout = { components: [] };
      }
      return form.value.catalogItemConfig.layout;
    default:
      return { components: [] };
  }
}

const activeLayoutConfig = computed({
  get() {
    switch (form.value.kind) {
      case 'CATEGORY':
        if (!form.value.categoryConfig.layout) {
          form.value.categoryConfig.layout = { components: [] };
        }
        return form.value.categoryConfig.layout;
      case 'PART':
        if (!form.value.partConfig.layout) {
          form.value.partConfig.layout = { components: [] };
        }
        return form.value.partConfig.layout;
      case 'CATALOG_ITEM':
        if (!form.value.catalogItemConfig.layout) {
          form.value.catalogItemConfig.layout = { components: [] };
        }
        return form.value.catalogItemConfig.layout;
      default:
        return { components: [] };
    }
  },
  set(value) {
    switch (form.value.kind) {
      case 'CATEGORY':
        form.value.categoryConfig.layout = value;
        break;
      case 'PART':
        form.value.partConfig.layout = value;
        break;
      case 'CATALOG_ITEM':
        form.value.catalogItemConfig.layout = value;
        break;
    }
  }
});

const activeAttributeConfig = computed({
  get() {
    switch (form.value.kind) {
      case 'CATEGORY':
        if (!form.value.categoryConfig.filtersConfig) {
          form.value.categoryConfig.filtersConfig = { groups: [], withUnits: false };
        }
        return form.value.categoryConfig.filtersConfig;
      case 'PART':
        if (!form.value.partConfig.attributeConfig) {
          form.value.partConfig.attributeConfig = { groups: [], withUnits: false };
        }
        return form.value.partConfig.attributeConfig;
      case 'CATALOG_ITEM':
        if (!form.value.catalogItemConfig.attributeConfig) {
          form.value.catalogItemConfig.attributeConfig = { groups: [], withUnits: false };
        }
        return form.value.catalogItemConfig.attributeConfig;
      default:
        return { groups: [], withUnits: false };
    }
  },
  set(value) {
    switch (form.value.kind) {
      case 'CATEGORY':
        form.value.categoryConfig.filtersConfig = value;
        break;
      case 'PART':
        form.value.partConfig.attributeConfig = value;
        break;
      case 'CATALOG_ITEM':
        form.value.catalogItemConfig.attributeConfig = value;
        break;
    }
  }
});

const activeSEOConfig = computed({
  get() {
    switch (form.value.kind) {
      case 'CATEGORY':
        if (!form.value.categoryConfig.seo) {
          form.value.categoryConfig.seo = {};
        }
        return form.value.categoryConfig.seo;
      case 'PART':
        if (!form.value.partConfig.seo) {
          form.value.partConfig.seo = {};
        }
        return form.value.partConfig.seo;
      case 'CATALOG_ITEM':
        if (!form.value.catalogItemConfig.seo) {
          form.value.catalogItemConfig.seo = {};
        }
        return form.value.catalogItemConfig.seo;
      default:
        return {};
    }
  },
  set(value) {
    switch (form.value.kind) {
      case 'CATEGORY':
        form.value.categoryConfig.seo = value;
        break;
      case 'PART':
        form.value.partConfig.seo = value;
        break;
      case 'CATALOG_ITEM':
        form.value.catalogItemConfig.seo = value;
        break;
    }
  }
});

function getActiveAttributeConfig() {
  switch (form.value.kind) {
    case 'CATEGORY':
      if (!form.value.categoryConfig.filtersConfig) {
        form.value.categoryConfig.filtersConfig = { groups: [], withUnits: false };
      }
      return form.value.categoryConfig.filtersConfig;
    case 'PART':
      if (!form.value.partConfig.attributeConfig) {
        form.value.partConfig.attributeConfig = { groups: [], withUnits: false };
      }
      return form.value.partConfig.attributeConfig;
    case 'CATALOG_ITEM':
      if (!form.value.catalogItemConfig.attributeConfig) {
        form.value.catalogItemConfig.attributeConfig = { groups: [], withUnits: false };
      }
      return form.value.catalogItemConfig.attributeConfig;
    default:
      return { groups: [], withUnits: false };
  }
}

function getActiveSEOConfig() {
  switch (form.value.kind) {
    case 'CATEGORY':
      if (!form.value.categoryConfig.seo) {
        form.value.categoryConfig.seo = {};
      }
      return form.value.categoryConfig.seo;
    case 'PART':
      if (!form.value.partConfig.seo) {
        form.value.partConfig.seo = {};
      }
      return form.value.partConfig.seo;
    case 'CATALOG_ITEM':
      if (!form.value.catalogItemConfig.seo) {
        form.value.catalogItemConfig.seo = {};
      }
      return form.value.catalogItemConfig.seo;
    default:
      return {};
  }
}

function getSEOPreviewData() {
  // Возвращаем тестовые данные для предпросмотра SEO
  return {
    entity: {
      name: 'Пример названия',
      description: 'Пример описания',
      partCategory: { name: 'Пример категории' }
    },
    attr: {
      brand: { value: 'Пример бренда' },
      model: { value: 'Пример модели' }
    },
    meta: {},
    var: {}
  };
}

function updateTemplate(updatedTemplate: PageTemplate) {
  form.value = { ...form.value, ...updatedTemplate };
}

// Инициализация
onMounted(() => {
  loadTemplate();
  loadAllCategories(); // Предзагрузка категорий для dropdown
});

// Отслеживание изменений формы для автообновления предпросмотра
watch(
  () => form.value,
  () => {
    if (showPreview.value) {
      // Дебаунс для предпросмотра
      clearTimeout(previewTimeout.value);
      previewTimeout.value = setTimeout(refreshPreview, 1000);
    }
  },
  { deep: true }
);

const previewTimeout = ref<NodeJS.Timeout | null>(null);
</script>
