/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryDefaultArgsObjectSchema } from './PartCategoryDefaultArgs.schema';
import { PartCategoryInputSchema } from '../input/PartCategoryInput.schema';
import { PartInputSchema } from '../input/PartInput.schema';
import { MediaAssetDefaultArgsObjectSchema } from './MediaAssetDefaultArgs.schema';
import { PageTemplateDefaultArgsObjectSchema } from './PageTemplateDefaultArgs.schema';
import { PartCategoryCountOutputTypeDefaultArgsObjectSchema } from './PartCategoryCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartCategorySelect>;
export const PartCategorySelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), name: z.boolean().optional().optional(), slug: z.boolean().optional().optional(), description: z.boolean().optional().optional(), parent: z.union([z.boolean(),
    z.lazy(() => PartCategoryDefaultArgsObjectSchema)]).optional(), parentId: z.boolean().optional().optional(), children: z.union([z.boolean(),
    z.lazy(() => PartCategoryInputSchema.findMany)]).optional(), level: z.boolean().optional().optional(), path: z.boolean().optional().optional(), icon: z.boolean().optional().optional(), parts: z.union([z.boolean(),
    z.lazy(() => PartInputSchema.findMany)]).optional(), imageId: z.boolean().optional().optional(), image: z.union([z.boolean(),
    z.lazy(() => MediaAssetDefaultArgsObjectSchema)]).optional(), pageTemplateId: z.boolean().optional().optional(), pageTemplate: z.union([z.boolean(),
    z.lazy(() => PageTemplateDefaultArgsObjectSchema)]).optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional(), _count: z.union([z.boolean(),
    z.lazy(() => PartCategoryCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
