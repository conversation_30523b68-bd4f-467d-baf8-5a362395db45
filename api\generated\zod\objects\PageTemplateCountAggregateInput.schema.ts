/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PageTemplateCountAggregateInputType>;
export const PageTemplateCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), name: z.literal(true).optional().optional(), description: z.literal(true).optional().optional(), kind: z.literal(true).optional().optional(), partCategoryId: z.literal(true).optional().optional(), isActive: z.literal(true).optional().optional(), isDefault: z.literal(true).optional().optional(), version: z.literal(true).optional().optional(), categoryConfig: z.literal(true).optional().optional(), partConfig: z.literal(true).optional().optional(), catalogItemConfig: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
