/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartUpdateWithoutPageTemplateInputObjectSchema } from './PartUpdateWithoutPageTemplateInput.schema';
import { PartUncheckedUpdateWithoutPageTemplateInputObjectSchema } from './PartUncheckedUpdateWithoutPageTemplateInput.schema';
import { PartCreateWithoutPageTemplateInputObjectSchema } from './PartCreateWithoutPageTemplateInput.schema';
import { PartUncheckedCreateWithoutPageTemplateInputObjectSchema } from './PartUncheckedCreateWithoutPageTemplateInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartUpsertWithWhereUniqueWithoutPageTemplateInput>;
export const PartUpsertWithWhereUniqueWithoutPageTemplateInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => PartUpdateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutPageTemplateInputObjectSchema)]), create: z.union([z.lazy(() => PartCreateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutPageTemplateInputObjectSchema)])
}).strict() as SchemaType;
