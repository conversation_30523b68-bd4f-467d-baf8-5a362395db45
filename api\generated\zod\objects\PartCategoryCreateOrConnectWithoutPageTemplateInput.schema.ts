/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereUniqueInputObjectSchema } from './PartCategoryWhereUniqueInput.schema';
import { PartCategoryCreateWithoutPageTemplateInputObjectSchema } from './PartCategoryCreateWithoutPageTemplateInput.schema';
import { PartCategoryUncheckedCreateWithoutPageTemplateInputObjectSchema } from './PartCategoryUncheckedCreateWithoutPageTemplateInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartCategoryCreateOrConnectWithoutPageTemplateInput>;
export const PartCategoryCreateOrConnectWithoutPageTemplateInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCategoryCreateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutPageTemplateInputObjectSchema)])
}).strict() as SchemaType;
