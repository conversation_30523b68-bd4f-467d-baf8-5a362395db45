/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';
import { IntFilterObjectSchema } from './IntFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { IntNullableFilterObjectSchema } from './IntNullableFilter.schema';
import { StringFilterObjectSchema } from './StringFilter.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartScalarWhereInput>;
export const PartScalarWhereInputObjectSchema: SchemaType = z.object({
    AND: z.union([z.lazy(() => PartScalarWhereInputObjectSchema),
    z.lazy(() => PartScalarWhereInputObjectSchema).array()]).optional(), OR: z.lazy(() => PartScalarWhereInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => PartScalarWhereInputObjectSchema),
    z.lazy(() => PartScalarWhereInputObjectSchema).array()]).optional(), createdAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), updatedAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), id: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), name: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), parentId: z.union([z.lazy(() => IntNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), level: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), path: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), imageId: z.union([z.lazy(() => IntNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), pageTemplateId: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), partCategoryId: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional()
}).strict() as SchemaType;
