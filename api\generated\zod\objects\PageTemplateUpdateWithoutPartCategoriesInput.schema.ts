/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { TemplateVersionSchema } from '../models/TemplateVersion.schema';

import { CategoryTemplateConfigSchema } from '../models/CategoryTemplateConfig.schema';

import { PartTemplateConfigSchema } from '../models/PartTemplateConfig.schema';

import { CatalogItemTemplateConfigSchema } from '../models/CatalogItemTemplateConfig.schema'; import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { TemplateKindSchema } from '../enums/TemplateKind.schema';
import { EnumTemplateKindFieldUpdateOperationsInputObjectSchema } from './EnumTemplateKindFieldUpdateOperationsInput.schema';
import { NullableIntFieldUpdateOperationsInputObjectSchema } from './NullableIntFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { DateTimeFieldUpdateOperationsInputObjectSchema } from './DateTimeFieldUpdateOperationsInput.schema';
import { PartUpdateManyWithoutPageTemplateNestedInputObjectSchema } from './PartUpdateManyWithoutPageTemplateNestedInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PageTemplateUpdateWithoutPartCategoriesInput>;
export const PageTemplateUpdateWithoutPartCategoriesInputObjectSchema: SchemaType = z.object({
    id: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), name: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), description: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), kind: z.union([z.lazy(() => TemplateKindSchema),
    z.lazy(() => EnumTemplateKindFieldUpdateOperationsInputObjectSchema)]).optional(), partCategoryId: z.union([z.number(),
    z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), isActive: z.union([z.boolean(),
    z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(), isDefault: z.union([z.boolean(),
    z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(), version: z.lazy(() => TemplateVersionSchema).optional(), categoryConfig: z.lazy(() => CategoryTemplateConfigSchema).optional(), partConfig: z.lazy(() => PartTemplateConfigSchema).optional(), catalogItemConfig: z.lazy(() => CatalogItemTemplateConfigSchema).optional(), createdAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), updatedAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), parts: z.lazy(() => PartUpdateManyWithoutPageTemplateNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
