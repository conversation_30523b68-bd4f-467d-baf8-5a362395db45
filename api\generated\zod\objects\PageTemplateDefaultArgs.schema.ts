/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PageTemplateSelectObjectSchema } from './PageTemplateSelect.schema';
import { PageTemplateIncludeObjectSchema } from './PageTemplateInclude.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PageTemplateDefaultArgs>;
export const PageTemplateDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => PageTemplateSelectObjectSchema).optional().optional(), include: z.lazy(() => PageTemplateIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
