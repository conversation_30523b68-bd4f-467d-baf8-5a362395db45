/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PageTemplateCountOutputTypeSelectObjectSchema } from './PageTemplateCountOutputTypeSelect.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PageTemplateCountOutputTypeDefaultArgs>;
export const PageTemplateCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => PageTemplateCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
