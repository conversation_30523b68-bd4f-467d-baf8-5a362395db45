/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartUpdateWithoutPageTemplateInputObjectSchema } from './PartUpdateWithoutPageTemplateInput.schema';
import { PartUncheckedUpdateWithoutPageTemplateInputObjectSchema } from './PartUncheckedUpdateWithoutPageTemplateInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartUpdateWithWhereUniqueWithoutPageTemplateInput>;
export const PartUpdateWithWhereUniqueWithoutPageTemplateInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => PartUpdateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutPageTemplateInputObjectSchema)])
}).strict() as SchemaType;
