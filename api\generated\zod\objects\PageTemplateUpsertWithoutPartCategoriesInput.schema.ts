/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PageTemplateUpdateWithoutPartCategoriesInputObjectSchema } from './PageTemplateUpdateWithoutPartCategoriesInput.schema';
import { PageTemplateUncheckedUpdateWithoutPartCategoriesInputObjectSchema } from './PageTemplateUncheckedUpdateWithoutPartCategoriesInput.schema';
import { PageTemplateCreateWithoutPartCategoriesInputObjectSchema } from './PageTemplateCreateWithoutPartCategoriesInput.schema';
import { PageTemplateUncheckedCreateWithoutPartCategoriesInputObjectSchema } from './PageTemplateUncheckedCreateWithoutPartCategoriesInput.schema';
import { PageTemplateWhereInputObjectSchema } from './PageTemplateWhereInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PageTemplateUpsertWithoutPartCategoriesInput>;
export const PageTemplateUpsertWithoutPartCategoriesInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => PageTemplateUpdateWithoutPartCategoriesInputObjectSchema), z.lazy(() => PageTemplateUncheckedUpdateWithoutPartCategoriesInputObjectSchema)]), create: z.union([z.lazy(() => PageTemplateCreateWithoutPartCategoriesInputObjectSchema), z.lazy(() => PageTemplateUncheckedCreateWithoutPartCategoriesInputObjectSchema)]), where: z.lazy(() => PageTemplateWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
