/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartScalarWhereInputObjectSchema } from './PartScalarWhereInput.schema';
import { PartUpdateManyMutationInputObjectSchema } from './PartUpdateManyMutationInput.schema';
import { PartUncheckedUpdateManyWithoutPageTemplateInputObjectSchema } from './PartUncheckedUpdateManyWithoutPageTemplateInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartUpdateManyWithWhereWithoutPageTemplateInput>;
export const PartUpdateManyWithWhereWithoutPageTemplateInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartScalarWhereInputObjectSchema), data: z.union([z.lazy(() => PartUpdateManyMutationInputObjectSchema), z.lazy(() => PartUncheckedUpdateManyWithoutPageTemplateInputObjectSchema)])
}).strict() as SchemaType;
