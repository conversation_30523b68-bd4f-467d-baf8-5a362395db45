/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryScalarWhereInputObjectSchema } from './PartCategoryScalarWhereInput.schema';
import { PartCategoryUpdateManyMutationInputObjectSchema } from './PartCategoryUpdateManyMutationInput.schema';
import { PartCategoryUncheckedUpdateManyWithoutPageTemplateInputObjectSchema } from './PartCategoryUncheckedUpdateManyWithoutPageTemplateInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartCategoryUpdateManyWithWhereWithoutPageTemplateInput>;
export const PartCategoryUpdateManyWithWhereWithoutPageTemplateInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryScalarWhereInputObjectSchema), data: z.union([z.lazy(() => PartCategoryUpdateManyMutationInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateManyWithoutPageTemplateInputObjectSchema)])
}).strict() as SchemaType;
