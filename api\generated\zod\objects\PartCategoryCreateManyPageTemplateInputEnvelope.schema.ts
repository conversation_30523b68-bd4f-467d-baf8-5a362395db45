/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryCreateManyPageTemplateInputObjectSchema } from './PartCategoryCreateManyPageTemplateInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartCategoryCreateManyPageTemplateInputEnvelope>;
export const PartCategoryCreateManyPageTemplateInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => PartCategoryCreateManyPageTemplateInputObjectSchema),
    z.lazy(() => PartCategoryCreateManyPageTemplateInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
