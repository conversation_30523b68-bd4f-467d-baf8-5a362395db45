<template>
  <div class="space-y-6">
    <!-- Заголовок -->
    <div>
      <h3 class="text-md font-medium">SEO настройки</h3>
      <p class="text-sm text-gray-600 mt-1">
        Настройте метаданные для поисковых систем с поддержкой переменных
      </p>
    </div>

    <!-- Основные SEO поля -->
    <VCard>
      <template #header>
        <h4 class="text-sm font-medium">Основные метаданные</h4>
      </template>
      <template #content>
        <div class="space-y-4">
          <!-- Title -->
          <div class="space-y-2">
            <label class="text-sm font-medium">Title (заголовок страницы)</label>
            <VInputText 
              v-model="localConfig.title"
              placeholder="Например: {{entity.name}} - {{entity.partCategory.name}}"
              class="w-full"
              :disabled="disabled"
            />
            <div class="flex items-center justify-between text-xs">
              <span class="text-gray-500">
                Рекомендуемая длина: 50-60 символов
              </span>
              <span :class="getTitleLengthClass()">
                {{ interpolatedTitle.length }} символов
              </span>
            </div>
            <div v-if="interpolatedTitle" class="p-2 bg-blue-50 rounded text-sm">
              <strong>Предпросмотр:</strong> {{ interpolatedTitle }}
            </div>
          </div>

          <!-- Description -->
          <div class="space-y-2">
            <label class="text-sm font-medium">Description (описание)</label>
            <VTextarea 
              v-model="localConfig.description"
              placeholder="Например: {{entity.description}} для {{attr.equipment_model.value}}"
              class="w-full min-h-[80px]"
              :disabled="disabled"
            />
            <div class="flex items-center justify-between text-xs">
              <span class="text-gray-500">
                Рекомендуемая длина: 150-160 символов
              </span>
              <span :class="getDescriptionLengthClass()">
                {{ interpolatedDescription.length }} символов
              </span>
            </div>
            <div v-if="interpolatedDescription" class="p-2 bg-blue-50 rounded text-sm">
              <strong>Предпросмотр:</strong> {{ interpolatedDescription }}
            </div>
          </div>

          <!-- Keywords -->
          <div class="space-y-2">
            <label class="text-sm font-medium">Keywords (ключевые слова)</label>
            <VInputChips 
              v-model="localConfig.keywords"
              placeholder="Добавьте ключевые слова или переменные"
              class="w-full"
              :disabled="disabled"
            />
            <div class="text-xs text-gray-500">
              Можно использовать переменные: {{attr?.brand?.value || 'brand'}}, {{entity.partCategory.name}}
            </div>
          </div>
        </div>
      </template>
    </VCard>

    <!-- Open Graph настройки -->
    <VCard>
      <template #header>
        <h4 class="text-sm font-medium">Open Graph (социальные сети)</h4>
      </template>
      <template #content>
        <div class="space-y-4">
          <!-- OG Title -->
          <div class="space-y-2">
            <label class="text-sm font-medium">OG Title</label>
            <VInputText 
              v-model="localConfig.ogTitle"
              placeholder="Оставьте пустым для использования основного title"
              class="w-full"
              :disabled="disabled"
            />
          </div>

          <!-- OG Description -->
          <div class="space-y-2">
            <label class="text-sm font-medium">OG Description</label>
            <VTextarea 
              v-model="localConfig.ogDescription"
              placeholder="Оставьте пустым для использования основного description"
              class="w-full min-h-[60px]"
              :disabled="disabled"
            />
          </div>

          <!-- OG Image -->
          <div class="space-y-2">
            <label class="text-sm font-medium">OG Image</label>
            <VInputText 
              v-model="localConfig.ogImage"
              placeholder="Например: {{entity.image.url}}"
              class="w-full"
              :disabled="disabled"
            />
            <div class="text-xs text-gray-500">
              URL изображения для социальных сетей (рекомендуемый размер: 1200x630px)
            </div>
          </div>
        </div>
      </template>
    </VCard>

    <!-- Справка по переменным -->
    <VCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h4 class="text-sm font-medium">Доступные переменные</h4>
          <VButton 
            :label="showVariables ? 'Скрыть' : 'Показать'"
            :icon="showVariables ? 'ChevronUp' : 'ChevronDown'"
            size="small"
            variant="outline"
            @click="showVariables = !showVariables"
          />
        </div>
      </template>
      <template #content>
        <div v-if="showVariables" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div 
              v-for="variable in availableVariables"
              :key="variable"
              class="flex items-center justify-between p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100"
              @click="insertVariable(variable)"
            >
              <code class="text-sm">{{ variable }}</code>
              <VButton 
                icon="Copy"
                size="small"
                variant="outline"
                @click.stop="copyVariable(variable)"
              />
            </div>
          </div>
          
          <div class="text-xs text-gray-500">
            Нажмите на переменную, чтобы скопировать её в буфер обмена
          </div>
        </div>
      </template>
    </VCard>

    <!-- Предпросмотр в поисковой выдаче -->
    <VCard v-if="interpolatedTitle || interpolatedDescription">
      <template #header>
        <h4 class="text-sm font-medium">Предпросмотр в поисковой выдаче</h4>
      </template>
      <template #content>
        <div class="p-4 bg-white border border-gray-200 rounded-lg">
          <div class="space-y-1">
            <div class="text-blue-600 text-lg hover:underline cursor-pointer">
              {{ interpolatedTitle || 'Заголовок страницы' }}
            </div>
            <div class="text-green-600 text-sm">
              https://example.com/page-url
            </div>
            <div class="text-gray-600 text-sm">
              {{ interpolatedDescription || 'Описание страницы для поисковых систем' }}
            </div>
          </div>
        </div>
      </template>
    </VCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import VCard from '@/volt/Card.vue';
import VButton from '@/volt/Button.vue';
import VInputText from '@/volt/InputText.vue';
import VTextarea from '@/volt/Textarea.vue';
import VInputChips from '@/volt/InputChips.vue';
import type { SEOConfig, TemplateKind, TemplateVariables } from '@/types/templates';
import { interpolateString, getAvailableVariables } from '@/utils/template-helpers';

interface Props {
  modelValue: SEOConfig;
  kind: TemplateKind;
  disabled?: boolean;
  previewData?: TemplateVariables;
}

interface Emits {
  (e: 'update:modelValue', value: SEOConfig): void;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
});

const emit = defineEmits<Emits>();

// Локальное состояние
const localConfig = ref<SEOConfig>({ ...props.modelValue });
const showVariables = ref(false);

// Доступные переменные для данного типа шаблона
const availableVariables = computed(() => getAvailableVariables(props.kind));

// Интерполированные значения для предпросмотра
const interpolatedTitle = computed(() => {
  if (!localConfig.value.title || !props.previewData) return localConfig.value.title || '';
  return interpolateString(localConfig.value.title, props.previewData);
});

const interpolatedDescription = computed(() => {
  if (!localConfig.value.description || !props.previewData) return localConfig.value.description || '';
  return interpolateString(localConfig.value.description, props.previewData);
});

// Классы для индикации длины
const getTitleLengthClass = () => {
  const length = interpolatedTitle.value.length;
  if (length < 30) return 'text-orange-500';
  if (length > 60) return 'text-red-500';
  return 'text-green-500';
};

const getDescriptionLengthClass = () => {
  const length = interpolatedDescription.value.length;
  if (length < 120) return 'text-orange-500';
  if (length > 160) return 'text-red-500';
  return 'text-green-500';
};

// Синхронизация с родительским компонентом
watch(localConfig, (newValue) => {
  emit('update:modelValue', newValue);
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
  localConfig.value = { ...newValue };
}, { deep: true });

// Методы
function insertVariable(variable: string) {
  copyVariable(variable);
}

function copyVariable(variable: string) {
  const template = `{{${variable}}}`;
  navigator.clipboard.writeText(template).then(() => {
    // Можно добавить уведомление об успешном копировании
  });
}
</script>
