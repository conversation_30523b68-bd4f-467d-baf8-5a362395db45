/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { AnyTRPCRouter as AnyRouter } from "@trpc/server";
import type { PrismaClient } from "@zenstackhq/runtime/models";
import { createTRPCRouter } from "../../../trpc";
import createMediaAssetRouter from "./MediaAsset.router";
import createAttributeGroupRouter from "./AttributeGroup.router";
import createAttributeTemplateRouter from "./AttributeTemplate.router";
import createAttributeSynonymGroupRouter from "./AttributeSynonymGroup.router";
import createAttributeSynonymRouter from "./AttributeSynonym.router";
import createPartAttributeRouter from "./PartAttribute.router";
import createCatalogItemAttributeRouter from "./CatalogItemAttribute.router";
import createEquipmentModelAttributeRouter from "./EquipmentModelAttribute.router";
import createEquipmentApplicabilityRouter from "./EquipmentApplicability.router";
import createPartRouter from "./Part.router";
import createPartApplicabilityRouter from "./PartApplicability.router";
import createCatalogItemRouter from "./CatalogItem.router";
import createMatchingProposalRouter from "./MatchingProposal.router";
import createPartCategoryRouter from "./PartCategory.router";
import createBrandRouter from "./Brand.router";
import createEquipmentModelRouter from "./EquipmentModel.router";
import createAggregateSchemaRouter from "./AggregateSchema.router";
import createSchemaPositionRouter from "./SchemaPosition.router";
import createSchemaAnnotationRouter from "./SchemaAnnotation.router";
import createPageTemplateRouter from "./PageTemplate.router";
import createUserRouter from "./User.router";
import createAccountRouter from "./Account.router";
import createSessionRouter from "./Session.router";

export function db(ctx: any) {
    if (!ctx.prisma) {
        throw new Error('Missing "prisma" field in trpc context');
    }
    return ctx.prisma as PrismaClient;
}

export function createRouter() {
    return createTRPCRouter({
        mediaAsset: createMediaAssetRouter(),
        attributeGroup: createAttributeGroupRouter(),
        attributeTemplate: createAttributeTemplateRouter(),
        attributeSynonymGroup: createAttributeSynonymGroupRouter(),
        attributeSynonym: createAttributeSynonymRouter(),
        partAttribute: createPartAttributeRouter(),
        catalogItemAttribute: createCatalogItemAttributeRouter(),
        equipmentModelAttribute: createEquipmentModelAttributeRouter(),
        equipmentApplicability: createEquipmentApplicabilityRouter(),
        part: createPartRouter(),
        partApplicability: createPartApplicabilityRouter(),
        catalogItem: createCatalogItemRouter(),
        matchingProposal: createMatchingProposalRouter(),
        partCategory: createPartCategoryRouter(),
        brand: createBrandRouter(),
        equipmentModel: createEquipmentModelRouter(),
        aggregateSchema: createAggregateSchemaRouter(),
        schemaPosition: createSchemaPositionRouter(),
        schemaAnnotation: createSchemaAnnotationRouter(),
        pageTemplate: createPageTemplateRouter(),
        user: createUserRouter(),
        account: createAccountRouter(),
        session: createSessionRouter(),
    }
    );
}
