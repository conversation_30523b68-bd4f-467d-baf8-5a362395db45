/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCreateManyPageTemplateInputObjectSchema } from './PartCreateManyPageTemplateInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartCreateManyPageTemplateInputEnvelope>;
export const PartCreateManyPageTemplateInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => PartCreateManyPageTemplateInputObjectSchema),
    z.lazy(() => PartCreateManyPageTemplateInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
