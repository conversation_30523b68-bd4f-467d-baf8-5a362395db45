/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCreateWithoutPageTemplateInputObjectSchema } from './PartCreateWithoutPageTemplateInput.schema';
import { PartUncheckedCreateWithoutPageTemplateInputObjectSchema } from './PartUncheckedCreateWithoutPageTemplateInput.schema';
import { PartCreateOrConnectWithoutPageTemplateInputObjectSchema } from './PartCreateOrConnectWithoutPageTemplateInput.schema';
import { PartUpsertWithWhereUniqueWithoutPageTemplateInputObjectSchema } from './PartUpsertWithWhereUniqueWithoutPageTemplateInput.schema';
import { PartCreateManyPageTemplateInputEnvelopeObjectSchema } from './PartCreateManyPageTemplateInputEnvelope.schema';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartUpdateWithWhereUniqueWithoutPageTemplateInputObjectSchema } from './PartUpdateWithWhereUniqueWithoutPageTemplateInput.schema';
import { PartUpdateManyWithWhereWithoutPageTemplateInputObjectSchema } from './PartUpdateManyWithWhereWithoutPageTemplateInput.schema';
import { PartScalarWhereInputObjectSchema } from './PartScalarWhereInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartUpdateManyWithoutPageTemplateNestedInput>;
export const PartUpdateManyWithoutPageTemplateNestedInputObjectSchema: SchemaType = z.object({
    create: z.union([z.lazy(() => PartCreateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartCreateWithoutPageTemplateInputObjectSchema).array(), z.lazy(() => PartUncheckedCreateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutPageTemplateInputObjectSchema).array()]).optional(), connectOrCreate: z.union([z.lazy(() => PartCreateOrConnectWithoutPageTemplateInputObjectSchema),
    z.lazy(() => PartCreateOrConnectWithoutPageTemplateInputObjectSchema).array()]).optional(), upsert: z.union([z.lazy(() => PartUpsertWithWhereUniqueWithoutPageTemplateInputObjectSchema),
    z.lazy(() => PartUpsertWithWhereUniqueWithoutPageTemplateInputObjectSchema).array()]).optional(), createMany: z.lazy(() => PartCreateManyPageTemplateInputEnvelopeObjectSchema).optional().optional(), set: z.union([z.lazy(() => PartWhereUniqueInputObjectSchema),
    z.lazy(() => PartWhereUniqueInputObjectSchema).array()]).optional(), disconnect: z.union([z.lazy(() => PartWhereUniqueInputObjectSchema),
    z.lazy(() => PartWhereUniqueInputObjectSchema).array()]).optional(), delete: z.union([z.lazy(() => PartWhereUniqueInputObjectSchema),
    z.lazy(() => PartWhereUniqueInputObjectSchema).array()]).optional(), connect: z.union([z.lazy(() => PartWhereUniqueInputObjectSchema),
    z.lazy(() => PartWhereUniqueInputObjectSchema).array()]).optional(), update: z.union([z.lazy(() => PartUpdateWithWhereUniqueWithoutPageTemplateInputObjectSchema),
    z.lazy(() => PartUpdateWithWhereUniqueWithoutPageTemplateInputObjectSchema).array()]).optional(), updateMany: z.union([z.lazy(() => PartUpdateManyWithWhereWithoutPageTemplateInputObjectSchema),
    z.lazy(() => PartUpdateManyWithWhereWithoutPageTemplateInputObjectSchema).array()]).optional(), deleteMany: z.union([z.lazy(() => PartScalarWhereInputObjectSchema),
    z.lazy(() => PartScalarWhereInputObjectSchema).array()]).optional()
}).strict() as SchemaType;
