/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereUniqueInputObjectSchema } from './PartCategoryWhereUniqueInput.schema';
import { PartCategoryUpdateWithoutPageTemplateInputObjectSchema } from './PartCategoryUpdateWithoutPageTemplateInput.schema';
import { PartCategoryUncheckedUpdateWithoutPageTemplateInputObjectSchema } from './PartCategoryUncheckedUpdateWithoutPageTemplateInput.schema';
import { PartCategoryCreateWithoutPageTemplateInputObjectSchema } from './PartCategoryCreateWithoutPageTemplateInput.schema';
import { PartCategoryUncheckedCreateWithoutPageTemplateInputObjectSchema } from './PartCategoryUncheckedCreateWithoutPageTemplateInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartCategoryUpsertWithWhereUniqueWithoutPageTemplateInput>;
export const PartCategoryUpsertWithWhereUniqueWithoutPageTemplateInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => PartCategoryUpdateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateWithoutPageTemplateInputObjectSchema)]), create: z.union([z.lazy(() => PartCategoryCreateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutPageTemplateInputObjectSchema)])
}).strict() as SchemaType;
