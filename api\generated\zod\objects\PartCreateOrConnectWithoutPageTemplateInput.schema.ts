/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartCreateWithoutPageTemplateInputObjectSchema } from './PartCreateWithoutPageTemplateInput.schema';
import { PartUncheckedCreateWithoutPageTemplateInputObjectSchema } from './PartUncheckedCreateWithoutPageTemplateInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartCreateOrConnectWithoutPageTemplateInput>;
export const PartCreateOrConnectWithoutPageTemplateInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCreateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutPageTemplateInputObjectSchema)])
}).strict() as SchemaType;
