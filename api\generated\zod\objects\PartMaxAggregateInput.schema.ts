/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartMaxAggregateInputType>;
export const PartMaxAggregateInputObjectSchema: SchemaType = z.object({
    createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), id: z.literal(true).optional().optional(), name: z.literal(true).optional().optional(), parentId: z.literal(true).optional().optional(), level: z.literal(true).optional().optional(), path: z.literal(true).optional().optional(), imageId: z.literal(true).optional().optional(), pageTemplateId: z.literal(true).optional().optional(), partCategoryId: z.literal(true).optional().optional()
}).strict() as SchemaType;
