/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PageTemplateCountOutputTypeSelect>;
export const PageTemplateCountOutputTypeSelectObjectSchema: SchemaType = z.object({
    parts: z.boolean().optional().optional(), partCategories: z.boolean().optional().optional()
}).strict() as SchemaType;
