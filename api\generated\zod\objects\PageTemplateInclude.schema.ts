/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartInputSchema } from '../input/PartInput.schema';
import { PartCategoryInputSchema } from '../input/PartCategoryInput.schema';
import { PageTemplateCountOutputTypeDefaultArgsObjectSchema } from './PageTemplateCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PageTemplateInclude>;
export const PageTemplateIncludeObjectSchema: SchemaType = z.object({
    parts: z.union([z.boolean(),
    z.lazy(() => PartInputSchema.findMany)]).optional(), partCategories: z.union([z.boolean(),
    z.lazy(() => PartCategoryInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => PageTemplateCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
