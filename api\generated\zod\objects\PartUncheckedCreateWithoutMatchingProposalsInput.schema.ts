/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartUncheckedCreateNestedManyWithoutParentInputObjectSchema } from './PartUncheckedCreateNestedManyWithoutParentInput.schema';
import { PartAttributeUncheckedCreateNestedManyWithoutPartInputObjectSchema } from './PartAttributeUncheckedCreateNestedManyWithoutPartInput.schema';
import { PartApplicabilityUncheckedCreateNestedManyWithoutPartInputObjectSchema } from './PartApplicabilityUncheckedCreateNestedManyWithoutPartInput.schema';
import { EquipmentApplicabilityUncheckedCreateNestedManyWithoutPartInputObjectSchema } from './EquipmentApplicabilityUncheckedCreateNestedManyWithoutPartInput.schema';
import { AggregateSchemaUncheckedCreateNestedManyWithoutPartInputObjectSchema } from './AggregateSchemaUncheckedCreateNestedManyWithoutPartInput.schema';
import { SchemaPositionUncheckedCreateNestedManyWithoutPartInputObjectSchema } from './SchemaPositionUncheckedCreateNestedManyWithoutPartInput.schema';
import { MediaAssetUncheckedCreateNestedManyWithoutPartsInputObjectSchema } from './MediaAssetUncheckedCreateNestedManyWithoutPartsInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartUncheckedCreateWithoutMatchingProposalsInput>;
export const PartUncheckedCreateWithoutMatchingProposalsInputObjectSchema: SchemaType = z.object({
    createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), id: z.number().optional().optional(), name: z.union([z.string(),
    z.null()]).optional().nullable(), parentId: z.union([z.number(),
    z.null()]).optional().nullable(), level: z.number().optional().optional(), path: z.string(), imageId: z.union([z.number(),
    z.null()]).optional().nullable(), pageTemplateId: z.union([z.string(),
    z.null()]).optional().nullable(), partCategoryId: z.number(), children: z.lazy(() => PartUncheckedCreateNestedManyWithoutParentInputObjectSchema).optional().optional(), attributes: z.lazy(() => PartAttributeUncheckedCreateNestedManyWithoutPartInputObjectSchema).optional().optional(), applicabilities: z.lazy(() => PartApplicabilityUncheckedCreateNestedManyWithoutPartInputObjectSchema).optional().optional(), equipmentApplicabilities: z.lazy(() => EquipmentApplicabilityUncheckedCreateNestedManyWithoutPartInputObjectSchema).optional().optional(), aggregateSchemas: z.lazy(() => AggregateSchemaUncheckedCreateNestedManyWithoutPartInputObjectSchema).optional().optional(), schemaPositions: z.lazy(() => SchemaPositionUncheckedCreateNestedManyWithoutPartInputObjectSchema).optional().optional(), mediaAssets: z.lazy(() => MediaAssetUncheckedCreateNestedManyWithoutPartsInputObjectSchema).optional().optional()
}).strict() as SchemaType;
