/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereUniqueInputObjectSchema } from './PartCategoryWhereUniqueInput.schema';
import { PartCategoryUpdateWithoutPageTemplateInputObjectSchema } from './PartCategoryUpdateWithoutPageTemplateInput.schema';
import { PartCategoryUncheckedUpdateWithoutPageTemplateInputObjectSchema } from './PartCategoryUncheckedUpdateWithoutPageTemplateInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartCategoryUpdateWithWhereUniqueWithoutPageTemplateInput>;
export const PartCategoryUpdateWithWhereUniqueWithoutPageTemplateInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => PartCategoryUpdateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateWithoutPageTemplateInputObjectSchema)])
}).strict() as SchemaType;
