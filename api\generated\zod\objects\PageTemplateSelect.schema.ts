/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartInputSchema } from '../input/PartInput.schema';
import { PartCategoryInputSchema } from '../input/PartCategoryInput.schema';
import { PageTemplateCountOutputTypeDefaultArgsObjectSchema } from './PageTemplateCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PageTemplateSelect>;
export const PageTemplateSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), name: z.boolean().optional().optional(), description: z.boolean().optional().optional(), kind: z.boolean().optional().optional(), partCategoryId: z.boolean().optional().optional(), isActive: z.boolean().optional().optional(), isDefault: z.boolean().optional().optional(), version: z.boolean().optional().optional(), categoryConfig: z.boolean().optional().optional(), partConfig: z.boolean().optional().optional(), catalogItemConfig: z.boolean().optional().optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional(), parts: z.union([z.boolean(),
    z.lazy(() => PartInputSchema.findMany)]).optional(), partCategories: z.union([z.boolean(),
    z.lazy(() => PartCategoryInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => PageTemplateCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
