<template>
  <div class="component-preview absolute -inset-1 bg-blue-100 rounded-lg opacity-0 transition-opacity">
    <!-- HeroImage -->
    <div v-if="component.component === 'HeroImage'" class="space-y-2">
      <div class="w-full h-48 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
        <div class="text-white text-center">
          <ImageIcon class="w-12 h-12 mx-auto mb-2" />
          <div class="text-lg font-medium">Главное изображение</div>
          <div class="text-sm opacity-80">{{ component.props?.aspectRatio || '16:9' }}</div>
        </div>
      </div>
    </div>

    <!-- MainTitle -->
    <div v-else-if="component.component === 'MainTitle'" class="space-y-2">
      <h1 class="text-3xl font-bold text-gray-900">
        {{ interpolateString(getTitle(), previewData) }}
      </h1>
    </div>

    <!-- Breadcrumbs -->
    <div v-else-if="component.component === 'Breadcrumbs'" class="space-y-2">
      <nav class="flex items-center space-x-2 text-sm text-gray-600">
        <span>Главная</span>
        <ChevronRightIcon class="w-4 h-4" />
        <span>Категория</span>
        <ChevronRightIcon class="w-4 h-4" />
        <span class="text-gray-900 font-medium">Текущая страница</span>
      </nav>
    </div>

    <!-- Description -->
    <div v-else-if="component.component === 'Description'" class="space-y-2">
      <div class="text-gray-700 leading-relaxed">
        {{ interpolateString(getDescription(), previewData) }}
      </div>
    </div>

    <!-- AttributeTable -->
    <div v-else-if="component.component === 'AttributeTable'" class="space-y-2">
      <div class="border border-gray-200 rounded-lg overflow-hidden">
        <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
          <h3 class="font-medium text-gray-900">Характеристики</h3>
        </div>
        <div class="divide-y divide-gray-200">
          <div v-for="attr in sampleAttributes" :key="attr.name" class="px-4 py-3 flex justify-between">
            <span class="text-gray-600">{{ attr.label }}</span>
            <span class="font-medium">{{ attr.value }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- AttributeGroups -->
    <div v-else-if="component.component === 'AttributeGroups'" class="space-y-4">
      <div v-for="group in sampleAttributeGroups" :key="group.title" class="border border-gray-200 rounded-lg">
        <div class="bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between">
          <h3 class="font-medium text-gray-900 flex items-center gap-2">
            <FolderIcon v-if="group.icon" class="w-4 h-4" />
            {{ group.title }}
          </h3>
          <ChevronDownIcon v-if="group.collapsible" class="w-4 h-4 text-gray-500" />
        </div>
        <div class="p-4 space-y-3">
          <div v-for="attr in group.attributes" :key="attr.name" class="flex justify-between">
            <span class="text-gray-600">{{ attr.label }}</span>
            <span class="font-medium">{{ attr.value }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- ImageGallery -->
    <div v-else-if="component.component === 'ImageGallery'" class="space-y-2">
      <div class="grid grid-cols-4 gap-2">
        <div v-for="i in 4" :key="i" class="aspect-square bg-gray-200 rounded-lg flex items-center justify-center">
          <ImageIcon class="w-6 h-6 text-gray-400" />
        </div>
      </div>
      <div class="text-sm text-gray-500 text-center">Галерея изображений</div>
    </div>

    <!-- TechnicalDrawing -->
    <div v-else-if="component.component === 'TechnicalDrawing'" class="space-y-2">
      <div
        class="w-full h-64 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
        <div class="text-center text-gray-500">
          <FileTextIcon class="w-12 h-12 mx-auto mb-2" />
          <div class="font-medium">Техническая схема</div>
          <div class="text-sm">Интерактивный чертеж</div>
        </div>
      </div>
    </div>

    <!-- CategoryFilters -->
    <div v-else-if="component.component === 'CategoryFilters'" class="space-y-2">
      <div class="flex flex-wrap gap-2">
        <div v-for="filter in sampleFilters" :key="filter"
          class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
          {{ filter }}
        </div>
      </div>
      <div class="text-sm text-gray-500">Фильтры категории</div>
    </div>

    <!-- ProductGrid -->
    <div v-else-if="component.component === 'ProductGrid'" class="space-y-2">
      <div class="grid grid-cols-3 gap-4">
        <div v-for="i in 6" :key="i" class="border border-gray-200 rounded-lg p-3">
          <div class="aspect-square bg-gray-100 rounded mb-2 flex items-center justify-center">
            <PackageIcon class="w-6 h-6 text-gray-400" />
          </div>
          <div class="text-sm font-medium">Товар {{ i }}</div>
          <div class="text-xs text-gray-500">Описание товара</div>
        </div>
      </div>
    </div>

    <!-- Неизвестный компонент -->
    <div v-else class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div class="flex items-center gap-2 text-yellow-800">
        <AlertTriangleIcon class="w-5 h-5" />
        <span class="font-medium">Неизвестный компонент: {{ component.component }}</span>
      </div>
      <div class="text-sm text-yellow-700 mt-1">
        Предпросмотр для этого компонента не реализован
      </div>

      <!-- Свойства компонента -->
      <div v-if="component.props && Object.keys(component.props).length > 0" class="mt-3">
        <div class="text-xs text-yellow-600 font-medium mb-1">Свойства:</div>
        <div class="text-xs text-yellow-700">
          {{ JSON.stringify(component.props, null, 2) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  ImageIcon, 
  ChevronRightIcon, 
  ChevronDownIcon,
  FolderIcon,
  FileTextIcon,
  PackageIcon,
  AlertTriangleIcon
} from 'lucide-vue-next';
import type { 
  LayoutComponent, 
  TemplateKind, 
  TemplateVariables 
} from '@/types/templates';
import { interpolateString } from '@/utils/template-helpers';

interface Props {
  component: LayoutComponent;
  kind: TemplateKind;
  config: any;
  previewData: TemplateVariables;
}

const props = defineProps<Props>();

// Примеры данных для предпросмотра
const sampleAttributes = [
  { name: 'brand', label: 'Бренд', value: 'Example Brand' },
  { name: 'model', label: 'Модель', value: 'Model X123' },
  { name: 'weight', label: 'Вес', value: '2.5 кг' },
  { name: 'material', label: 'Материал', value: 'Сталь' }
];

const sampleAttributeGroups = [
  {
    title: 'Основные характеристики',
    collapsible: true,
    icon: true,
    attributes: [
      { name: 'brand', label: 'Бренд', value: 'Example Brand' },
      { name: 'model', label: 'Модель', value: 'Model X123' }
    ]
  },
  {
    title: 'Технические параметры',
    collapsible: true,
    icon: false,
    attributes: [
      { name: 'weight', label: 'Вес', value: '2.5 кг' },
      { name: 'material', label: 'Материал', value: 'Сталь' }
    ]
  }
];

const sampleFilters = ['Бренд', 'Цена', 'Наличие', 'Рейтинг'];

// Методы
function getTitle(): string {
  switch (props.kind) {
    case 'CATEGORY':
      return (props.config as any)?.h1 || '{{entity.name}}';
    case 'PART':
      return (props.config as any)?.h1 || '{{entity.name}}';
    case 'CATALOG_ITEM':
      return (props.config as any)?.h1 || '{{entity.name}}';
    default:
      return '{{entity.name}}';
  }
}

function getDescription(): string {
  switch (props.kind) {
    case 'CATEGORY':
      return (props.config as any)?.description || '{{entity.description}}';
    case 'PART':
      return '{{entity.description}}';
    case 'CATALOG_ITEM':
      return '{{entity.description}}';
    default:
      return '{{entity.description}}';
  }
}
</script>

