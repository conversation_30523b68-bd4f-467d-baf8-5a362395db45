/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCreateWithoutPageTemplateInputObjectSchema } from './PartCreateWithoutPageTemplateInput.schema';
import { PartUncheckedCreateWithoutPageTemplateInputObjectSchema } from './PartUncheckedCreateWithoutPageTemplateInput.schema';
import { PartCreateOrConnectWithoutPageTemplateInputObjectSchema } from './PartCreateOrConnectWithoutPageTemplateInput.schema';
import { PartCreateManyPageTemplateInputEnvelopeObjectSchema } from './PartCreateManyPageTemplateInputEnvelope.schema';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PartUncheckedCreateNestedManyWithoutPageTemplateInput>;
export const PartUncheckedCreateNestedManyWithoutPageTemplateInputObjectSchema: SchemaType = z.object({
    create: z.union([z.lazy(() => PartCreateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartCreateWithoutPageTemplateInputObjectSchema).array(), z.lazy(() => PartUncheckedCreateWithoutPageTemplateInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutPageTemplateInputObjectSchema).array()]).optional(), connectOrCreate: z.union([z.lazy(() => PartCreateOrConnectWithoutPageTemplateInputObjectSchema),
    z.lazy(() => PartCreateOrConnectWithoutPageTemplateInputObjectSchema).array()]).optional(), createMany: z.lazy(() => PartCreateManyPageTemplateInputEnvelopeObjectSchema).optional().optional(), connect: z.union([z.lazy(() => PartWhereUniqueInputObjectSchema),
    z.lazy(() => PartWhereUniqueInputObjectSchema).array()]).optional()
}).strict() as SchemaType;
