/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { TemplateVersionSchema } from '../models/TemplateVersion.schema';

import { CategoryTemplateConfigSchema } from '../models/CategoryTemplateConfig.schema';

import { PartTemplateConfigSchema } from '../models/PartTemplateConfig.schema';

import { CatalogItemTemplateConfigSchema } from '../models/CatalogItemTemplateConfig.schema'; import { z } from 'zod';
import { PageTemplateWhereInputObjectSchema } from './PageTemplateWhereInput.schema';
import { PageTemplateUpdateWithoutPartCategoriesInputObjectSchema } from './PageTemplateUpdateWithoutPartCategoriesInput.schema';
import { PageTemplateUncheckedUpdateWithoutPartCategoriesInputObjectSchema } from './PageTemplateUncheckedUpdateWithoutPartCategoriesInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PageTemplateUpdateToOneWithWhereWithoutPartCategoriesInput>;
export const PageTemplateUpdateToOneWithWhereWithoutPartCategoriesInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PageTemplateWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => PageTemplateUpdateWithoutPartCategoriesInputObjectSchema), z.lazy(() => PageTemplateUncheckedUpdateWithoutPartCategoriesInputObjectSchema)])
}).strict() as SchemaType;
