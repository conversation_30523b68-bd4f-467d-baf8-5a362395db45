/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { TemplateVersionSchema } from '../models/TemplateVersion.schema';

import { CategoryTemplateConfigSchema } from '../models/CategoryTemplateConfig.schema';

import { PartTemplateConfigSchema } from '../models/PartTemplateConfig.schema';

import { CatalogItemTemplateConfigSchema } from '../models/CatalogItemTemplateConfig.schema'; import { z } from 'zod';
import { TemplateKindSchema } from '../enums/TemplateKind.schema';
import { PartUncheckedCreateNestedManyWithoutPageTemplateInputObjectSchema } from './PartUncheckedCreateNestedManyWithoutPageTemplateInput.schema';

import type { Prisma } from '@zenstackhq/runtime/models';

type SchemaType = z.ZodType<Prisma.PageTemplateUncheckedCreateWithoutPartCategoriesInput>;
export const PageTemplateUncheckedCreateWithoutPartCategoriesInputObjectSchema: SchemaType = z.object({
    id: z.string().optional().optional(), name: z.string(), description: z.union([z.string(),
    z.null()]).optional().nullable(), kind: z.lazy(() => TemplateKindSchema), partCategoryId: z.union([z.number(),
    z.null()]).optional().nullable(), isActive: z.boolean().optional().optional(), isDefault: z.boolean().optional().optional(), version: z.lazy(() => TemplateVersionSchema).optional(), categoryConfig: z.lazy(() => CategoryTemplateConfigSchema).optional(), partConfig: z.lazy(() => PartTemplateConfigSchema).optional(), catalogItemConfig: z.lazy(() => CatalogItemTemplateConfigSchema).optional(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), parts: z.lazy(() => PartUncheckedCreateNestedManyWithoutPageTemplateInputObjectSchema).optional().optional()
}).strict() as SchemaType;
